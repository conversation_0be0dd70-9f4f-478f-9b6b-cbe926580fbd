from collections import defaultdict
import dataclasses
import time
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, List, Tuple
from dotenv import load_dotenv

# Import logging configuration
from apis.llm.data_types import COMPLETED_BATCH_STATUSES, BatchResponse, BatchStatus, CompletionRequest, CompletionResponse, CompletionStatus
from nlp.utils import estimate_tokens
from utils.logging_config import configure_logging

# Configure logger for this module
logger = configure_logging(__name__, log_file='llm_base.log')

load_dotenv()


class BaseAPIManager(ABC):
    """
    Abstract base class for API managers with common functionality.
    Features:
    - Rate limiting (requests per minute)
    - Budget tracking
    - Request logging
    - Error handling and retries
    """

    total_budget = 0.0
    total_cost = 0.0
    provider_costs = defaultdict(float)

    @classmethod
    def set_budget(cls, total_budget: float, cost: float = 0.0) -> None:
        cls.total_budget = total_budget
        cls.total_cost = cost

    @classmethod
    def check_budget(cls, estimated_cost: float) -> bool:
        """Check if the request would exceed the budget limit."""
        if cls.total_budget is None:
            return True

        if cls.total_cost + estimated_cost > cls.total_budget:
            logger.critical(
                f"Request would exceed budget limit. Current: ${cls.total_cost:.4f}, "
                f"Limit: ${cls.total_budget:.4f}"
            )
            return False
        return True

    @classmethod
    def add_cost(cls, cost: float, provider: Optional[str] = None) -> None:
        cls.total_cost += cost
        cls.provider_costs[provider] += cost

    @classmethod
    def get_total_cost(cls) -> float:
        return cls.total_cost

    @classmethod
    def get_budget_status(cls):
        status = {
            'total_spent': cls.total_cost,
        }
        if cls.total_budget:
            status['total_budget'] = cls.total_budget
            status['total_remaining'] = cls.total_budget - cls.total_cost

        if cls.provider_costs:
            status['providers'] = {}
            for privider, cost in cls.provider_costs.items():
                status['providers'][privider] = cost

        return status

    def __init__(
        self,
        requests_per_minute: int = 10,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        model_pricing: Dict[str, Dict[str, float]] = None,
        batch_discount: float = 0.5,
    ):
        """
        Initialize the API manager.

        Args:
            requests_per_minute: Maximum requests per minute
            max_retries: Maximum number of retries for failed requests
            retry_delay: Delay between retries in seconds
            model_pricing: Pricing information for models
        """
        self.requests_per_minute = requests_per_minute
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.model_pricing = model_pricing or {}
        self.batch_discount = batch_discount

        # Rate limiting
        self.request_times: List[float] = []
        self.total_estimated_cost = 0.0

    @abstractmethod
    def _initialize_client(self):
        """Initialize the API client."""
        pass

    @abstractmethod
    def _make_completion_request(self, request: CompletionRequest) -> CompletionResponse:
        """Make the actual API request for completion."""
        pass

    @abstractmethod
    def _create_batch_request(self, requests: List[CompletionRequest]) -> BatchResponse:
        """Create a batch request."""
        pass

    @abstractmethod
    def _retrieve_batch_status_request(self, batch_id: str) -> BatchResponse:
        """Retrieve batch status."""
        pass

    @abstractmethod
    def _retrieve_batch_results_request(self, batch_id: str) -> List[CompletionResponse]:
        """Retrieve batch results."""
        pass

    @abstractmethod
    def _list_batches_request(self, limit: int) -> List[BatchResponse]:
        """List all Message Batches."""
        pass

    def _check_rate_limit(self):
        """Check and enforce rate limiting."""
        current_time = time.time()
        # Remove requests older than 1 minute
        self.request_times = [
            t for t in self.request_times if current_time - t < 60]

        if len(self.request_times) >= self.requests_per_minute:
            sleep_time = 60 - (current_time - self.request_times[0])
            if sleep_time > 0:
                logger.info(
                    f"Rate limit reached. Sleeping for {sleep_time:.2f} seconds")
                time.sleep(sleep_time)
            self.request_times = self.request_times[1:]

    def _estimate_cost(self, model: str, prompt_tokens: int, completion_tokens: int, is_batch: bool = False) -> float:
        """Estimate the cost of a request based on token counts."""
        base_model = next(
            (key for key in self.model_pricing if model.startswith(key + "-")),
            None
        )
        if not base_model:
            logger.warning(f"Pricing not available for model {model}")
            return 0.0

        input_rate = self.model_pricing[base_model]['input_rate']
        output_rate = self.model_pricing[base_model]['output_rate']

        input_cost = (prompt_tokens / 1000) * input_rate
        output_cost = (completion_tokens / 1000) * output_rate

        total_cost = input_cost + output_cost

        return total_cost * self.batch_discount if is_batch else total_cost

    def get_completion(self, request: CompletionRequest) -> CompletionResponse | None:
        """
        Get a completion from the API with rate limiting and budget control.
        """
        # Check rate limit
        self._check_rate_limit()

        # Estimate cost (rough estimate based on prompt length)
        prompt_text = (request.user_prompt or "") + \
            (request.system_prompt or "")
        estimated_tokens = estimate_tokens(prompt_text)
        estimated_cost = self._estimate_cost(
            request.model, estimated_tokens, request.max_tokens)

        if not self.check_budget(estimated_cost):
            raise ValueError("Request would exceed budget limit")

        # Make the request with retries
        for attempt in range(self.max_retries):
            try:
                completion = self._make_completion_request(request)

                # Update request tracking
                self.request_times.append(time.time())

                # Calculate actual cost
                actual_cost = self._estimate_cost(
                    request.model,
                    completion.input_tokens,
                    completion.output_tokens
                )
                self.add_cost(actual_cost, self.__class__.__name__)
                completion.cost = actual_cost

                # Log the request
                logger.info(
                    f"Request completed. Cost: ${actual_cost:.4f}, "
                    f"Total cost: ${self.get_total_cost():.4f}"
                )

                return completion

            except Exception as e:
                if attempt == self.max_retries - 1:
                    logger.error(
                        f"Failed after {self.max_retries} attempts: {str(e)}")
                    return None
                logger.warning(f"Attempt {attempt + 1} failed: {str(e)}")
                time.sleep(self.retry_delay * (attempt + 1))

    def get_completion_batch(self, requests: List[CompletionRequest]) -> BatchResponse | None:
        batch_estimate_tokens = 0
        batch_estimated_cost = 0.0
        for request in requests:
            # Estimate cost (rough estimate based on prompt length)
            estimated_tokens = estimate_tokens(
                (request.user_prompt or "") + (request.system_prompt or ""))
            estimated_cost = self._estimate_cost(
                request.model, estimated_tokens, request.max_tokens, is_batch=True)
            request.estimated_tokens = estimated_tokens
            request.estimated_cost = estimated_cost

            batch_estimate_tokens += estimated_tokens
            batch_estimated_cost += estimated_cost

        self.total_estimated_cost += batch_estimated_cost
        logger.info(
            f"Batch estimated cost: ${batch_estimated_cost:.4f}, "
            f"Total estimated cost: ${self.total_estimated_cost:.4f}"
            f"Current cost: ${self.total_cost:.4f}, "
            f"Total budget: ${self.total_budget:.4f}"
        )

        if not self.check_budget(self.total_estimated_cost):
            raise ValueError("Request would exceed budget limit")

        # Check rate limit
        self._check_rate_limit()

        try:
            batch_response = self._create_batch_request(requests)

            # Update request tracking
            self.request_times.append(time.time())

            # Add estimated cost and tokens
            batch_response.estimated_tokens = batch_estimate_tokens
            batch_response.estimated_cost = batch_estimated_cost

            logger.info(f"Batch request created {batch_response.id}")
            return batch_response
        except Exception as e:
            logger.error(f"Batch request failed. Error {e}")
            return None

    def retrieve_batch(self, batch_id: str, fetch_results: bool = True) -> BatchResponse | None:
        """Retrieve the status of a batch request."""
        try:
            batch_response = self._retrieve_batch_status_request(batch_id)
            if fetch_results and batch_response.status in COMPLETED_BATCH_STATUSES:
                batch_results = self._retrieve_batch_results(batch_id)
                batch_response.cost = sum([r.cost for r in batch_results])
                batch_response.input_tokens = sum(
                    [r.input_tokens for r in batch_results])
                batch_response.output_tokens = sum(
                    [r.output_tokens for r in batch_results])
                batch_response.completion_results = batch_results

                self.total_cost += batch_response.cost
                # Log the request
                logger.info(
                    f"Batch request completed. Cost: ${batch_response.cost:.4f}, "
                    f"Total cost: ${self.total_cost:.4f}"
                )

            return batch_response
        except Exception as e:
            logger.error(
                f"Failed to retrieve status for batch {batch_id}. Error {e}")
            return None

    def _retrieve_batch_results(self, batch_id: str) -> List[CompletionResponse]:
        """Retrieve the results of a completed batch request."""
        try:
            batch_results = self._retrieve_batch_results_request(batch_id)
            for result in batch_results:
                # Calculate actual cost
                if result.status == CompletionStatus.SUCCEEDED.value:
                    actual_cost = self._estimate_cost(
                        result.model, result.input_tokens, result.output_tokens, is_batch=True)
                    result.cost = actual_cost
                else:
                    logger.error(f"Failed completion result {result}")

            return batch_results
        except Exception as e:
            logger.error(
                f"Failed to retrieve results for batch {batch_id}. Error {e}")
            return []
