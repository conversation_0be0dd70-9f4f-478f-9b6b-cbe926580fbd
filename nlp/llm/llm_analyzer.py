import json
import argparse
from typing import Optional, Dict, Any

from db.database import <PERSON>Manager
from apis.llm.base import BaseAPIManager
from apis.llm.data_types import CompletionRequest, CompletionStatus
from apis.llm.anthropic import AnthropicManager
from apis.llm.openai import OpenAIManager

from nlp.llm.prompt import PromptManager
from nlp.llm.helpers import TZ, LLMConfig, extract_article_id, generate_custom_id, process_article

# Configure logging
from utils.logging_config import configure_logging
logger = configure_logging(__name__, log_file='llm_analyzer.log')


class LLMAnalyzer:
    """
    Main class for analyzing news articles using LLM APIs.

    Provides both single article analysis and batch processing capabilities
    with proper error handling, caching, and structured output storage.
    """

    def __init__(self, api_name: str, config: LLMConfig, db: DatabaseManager):
        """Initialize the news analyzer with configuration."""
        self.config = config
        self.db = db
        self.prompt_manager = PromptManager()
        # Initialize LLM API
        self.api_name = api_name
        self.llm_api = self._initialize_llm_api(api_name)

        logger.info("LLMAnalyzer initialized successfully")

    def _initialize_llm_api(self, api_name: str) -> BaseAPIManager:
        """Initialize the appropriate LLM API based on configuration."""
        api_mapping = {
            'openai': OpenAIManager,
            'anthropic': AnthropicManager
        }

        api_class = api_mapping.get(api_name)
        if not api_class:
            raise ValueError(f"Unsupported API: {api_name}")

        return api_class(
            requests_per_minute=self.config.requests_per_minute
        )

    def save_completion(self, result_dict: Dict[str, Any], api_name: str, prompt_type: str) -> Optional[Dict[str, Any]]:
        """Save completion result to database."""
        try:
            article_id = extract_article_id(result_dict['custom_id'])
            db_record = {
                "id": result_dict['custom_id'],
                'article_id': article_id,
                "api": api_name,
                "prompt_type": prompt_type,
                "model": result_dict['model'],
                "status": result_dict['status'],
                "cost": result_dict.get('cost') or result_dict.get('estimated_cost'),
                "raw_response": result_dict.get('raw_response')
            }
            if 'user_prompt' in result_dict and 'system_prompt' in result_dict:
                db_record['prompt'] = result_dict['user_prompt'] + \
                    result_dict['system_prompt']

            if 'content' in result_dict:
                parsed_result = self.prompt_manager.parse_result(
                    result_dict['content'], prompt_type)
                db_record['content'] = parsed_result

                # Save to article metadata
                if self.db.article_service.add_article_metadata(
                        article_id, {prompt_type: db_record['content']}):
                    logger.info(
                        f"Successfully saved in article metadata: {article_id} ")
                else:
                    logger.error(
                        f"Failed to save in article metadata: {article_id}")

            if self.db.llm_api_service.upsert_llm_result(db_record):
                logger.info(
                    f"Successfully saved result: {result_dict['custom_id']}")
            else:
                logger.error(
                    f"Failed to save result: {result_dict['custom_id']}")

            return db_record
        except Exception as e:
            logger.error(f"Error saving result: {e}")

        return None

    def analyze_article(self, article: Dict[str, Any], prompt_type: str, api_name: str, model: str) -> Dict[str, Any]:
        """Analyze a single article using the specified prompt type."""
        try:
            # Process article input
            article_input = process_article(article)
            if not article_input:
                return {}

            # Get and format prompt
            prompt = self.prompt_manager.get_prompt(prompt_type)
            system_prompt = prompt['system_prompt']
            formatted_prompt = prompt['prompt_template'].format(
                title=article_input['title'],
                content=article_input['content']
            )

            request = CompletionRequest(
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                user_prompt=formatted_prompt,
                system_prompt=system_prompt,
                model=model
            )

            # Call LLM API
            completion = self.llm_api.get_completion(request)

            if not completion or completion.status != CompletionStatus.SUCCEEDED.value or not completion.content:
                logger.error(
                    f"Failed to get completion for article {article['id']}")
                return {}

            completion.custom_id = generate_custom_id(
                article['id'], prompt_type, api_name)
            self.save_completion(completion.to_dict(), api_name, prompt_type)

            return {prompt_type: completion.content}
        except Exception as e:
            logger.error(
                f"Error analyzing article {article.get('id', 'unknown')}: {e}")
        return {}

    def analyze_article_by_url(self, api_name: str, url: str, prompt_type: str, model: str) -> Dict[str, Any]:
        """Analyze a specific article by URL."""
        try:
            article = self.db.article_service.get_article_by_url(url)
            if not article:
                logger.warning(f"Article not found: {url}")
                return {}

            rid = generate_custom_id(
                article['id'], prompt_type, api_name)

            # Check if already analyzed
            existing_result = self.db.llm_api_service.get_result_by_id(rid)
            if existing_result:
                logger.info(f"Returning existing result for {url}")
                return {prompt_type: existing_result['content']}

            return self.analyze_article(article, prompt_type, api_name, model)

        except Exception as e:
            logger.error(f"Error analyzing article by URL {url}: {e}")

        return {}


def main():
    """Main function to run the analyzer from command line."""
    parser = argparse.ArgumentParser(
        description='Analyze news articles using LLM')

    # Basic options
    parser.add_argument('-u', '--url', help='URL of article to analyze')
    parser.add_argument('-a', '--api', default='openai', help='LLM API name')
    parser.add_argument(
        '-m', '--model', default='gpt-4.1-nano', help='LLM model to use')
    parser.add_argument('-t', '--prompt-type',
                        default='influence', help='Type of analysis to perform')

    # Resource limits
    parser.add_argument('-b', '--budget', type=float, default=0.1,
                        help='Maximum budget for API calls')
    parser.add_argument('--max-input', type=int, default=2048,
                        help='Maximum input words')
    parser.add_argument('--max-tokens', type=int, default=512,
                        help='Maximum output tokens')
    parser.add_argument('--min-input', type=int, default=200,
                        help='Minimum input words')
    parser.add_argument('--requests-per-minute', type=int, default=10,
                        help='Rate limit per minute')

    args = parser.parse_args()

    try:
        # Initialize analyzer
        llm_config = LLMConfig(
            max_tokens=args.max_tokens,
            max_input=args.max_input,
            min_input=args.min_input,
            temperature=0.7,
            requests_per_minute=args.requests_per_minute
        )
        db_manager = DatabaseManager()
        analyzer = LLMAnalyzer(args.api, llm_config, db_manager)

        current_total_cost = db_manager.llm_api_service.get_total_cost(
            prompt_type=args.prompt_type)
        if current_total_cost >= args.budget:
            logger.warning(f"Current cost {current_total_cost} exceeds budget limit "
                           f"{args.budget} for prompt type {args.prompt_type}.")
        BaseAPIManager.set_budget(args.budget, current_total_cost)

        article = analyzer.db.article_service.get_article_by_url(args.url)
        if not article:
            logger.error(f"Article not found: {args.url}")

        result = analyzer.analyze_article(
            article, args.prompt_type, args.api, args.model)
        if result:
            print(json.dumps(result, indent=2, default=str))
        else:
            print(f"Analysis failed for URL: {args.url}")

    except Exception as e:
        logger.error(f"Application error: {e}")


if __name__ == '__main__':
    main()
