import argparse
import async<PERSON>
from datetime import date, datetime, timedelta
from typing import Any, Dict, List, Optional, Set
from contextlib import asynccontextmanager
from dataclasses import dataclass

from apis.llm.anthropic import AnthropicManager
from apis.llm.base import BaseAPIManager
from apis.llm.data_types import ACTIVE_BATCH_STATUSES, COMPLETED_BATCH_STATUSES, INCOMPLETED_BATCH_STATUSES, BatchStatus, CompletionRequest, CompletionStatus
from apis.llm.openai import OpenAIManager
from apis.yahoo_finance import yahoo_api

from db.database import DatabaseManager
from nlp.llm.prompt import PromptManager
from nlp.llm.helpers import TZ, LLMConfig, extract_article_id, generate_custom_id, process_article

# Configure logging
from utils.logging_config import configure_logging
logger = configure_logging(__name__, log_file='llm_analyzer_v2.log')


@dataclass
class AsyncBatchConfig:
    """Configuration for async batch processing v2."""
    # Batch processing
    batch_size: int = 20
    max_articles_per_cycle: int = 1000

    # Queue configuration
    queue_maxsize: int = 100

    # Polling intervals (seconds)
    batch_creation_interval: int = 60  # How often to check for new articles
    batch_monitoring_interval: int = 300  # How often to check batch status

    # Retry and backoff configuration
    max_retries: int = 3
    initial_backoff_delay: float = 1.0  # Initial delay in seconds
    max_backoff_delay: float = 300.0  # Maximum delay in seconds
    backoff_multiplier: float = 2.0  # Exponential backoff multiplier

    # API priority configuration
    openai_priority_delay: float = 0.1  # Delay to give OpenAI priority
    anthropic_delay: float = 1.0  # Additional delay for Anthropic

    # Timeout configuration
    api_request_timeout: float = 30.0  # Timeout for individual API requests
    batch_creation_timeout: float = 120.0  # Timeout for batch creation

    # Resource limits
    max_concurrent_batches: int = 50  # Maximum active batches per API


API_MODELS = {
    'openai': 'gpt-4o-mini',
    'anthropic': 'claude-3-5-haiku'
}


def get_target_dates(
    start_date: datetime,
    end_date: datetime,
    threshold: float = 0.01,
    interval: str = "1d",
    days_before: int = 3,
    days_after: int = 3,
) -> Set[date]:
    """
    Fetch dates where SPY's absolute daily return exceeded the given threshold.

    Args:
        start_date: Start date for analysis.
        end_date: End date for analysis.
        threshold: Minimum absolute daily return (e.g., 0.01 for 1%).
        interval: Data interval, default is "1d".
        days_before: How many days before the target date to include.
        days_after: How many days after the target date to include.

    Returns:
        Set of target dates (datetime.date).
    """
    try:
        df = yahoo_api.get_price_data(
            ticker="SPY",
            start_date=start_date,
            end_date=end_date,
            interval=interval
        )
    except Exception as e:
        logger.error(f"Error fetching price data: {e}")
        return set()

    if df.empty or "Close" not in df.columns:
        logger.warning("No price data available for the specified period")
        return set()

    df["return"] = df["Close"].pct_change()
    mask = df["return"].abs() > threshold
    filtered = df[mask]

    result = set()
    for ts in filtered.index:
        base_date = ts.replace(tzinfo=TZ).date()
        for offset in range(-days_before, days_after + 1):
            result.add(base_date + timedelta(days=offset))

    return result


class BatchItem:
    """Represents a batch item in the processing queue."""

    def __init__(self, articles: List[Dict[str, Any]], prompt_type: str, batch_id: str):
        self.articles = articles
        self.prompt_type = prompt_type
        self.batch_id = batch_id
        self.created_at = datetime.now()
        self.retry_count = 0

    def __len__(self):
        return len(self.articles)


class LLMBatchAnalyzerV2:
    """
    Advanced async batch processor with queue-based architecture.

    Features:
    - Four concurrent async components
    - Queue-based batch distribution
    - API priority handling (OpenAI first, then Anthropic)
    - Exponential backoff with configurable parameters
    - Proper async context management
    - Enhanced error handling and recovery
    """

    def __init__(self, llm_config: LLMConfig, async_config: AsyncBatchConfig, db: DatabaseManager):
        self.llm_config = llm_config
        self.async_config = async_config
        self.db = db
        self.prompt_manager = PromptManager()
        self.llm_apis = self._initialize_llm_apis()

        # Async coordination
        self.batch_queue: asyncio.Queue = asyncio.Queue(
            maxsize=async_config.queue_maxsize)
        self.running = True
        self.shutdown_event = asyncio.Event()

        # Tracking
        self.active_batches: Dict[str, Set[str]] = {
            'openai': set(), 'anthropic': set()}
        self.stats = {
            'batches_created': 0,
            'batches_completed': 0,
            'batches_failed': 0,
            'articles_processed': 0
        }

    def _initialize_llm_apis(self) -> Dict[str, BaseAPIManager]:
        """Initialize the LLM APIs."""
        api_mapping = {
            'openai': OpenAIManager,
            'anthropic': AnthropicManager
        }

        llm_apis = {}
        for api_name, api_class in api_mapping.items():
            llm_apis[api_name] = api_class(
                requests_per_minute=self.llm_config.requests_per_minute
            )
        return llm_apis

    async def _calculate_backoff_delay(self, retry_count: int) -> float:
        """Calculate exponential backoff delay."""
        delay = self.async_config.initial_backoff_delay * (
            self.async_config.backoff_multiplier ** retry_count
        )
        return min(delay, self.async_config.max_backoff_delay)

    def get_article_candidates(
        self,
        prompt_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        max_articles: Optional[int] = None,
        filter_target_dates: bool = False
    ) -> List[Dict[str, Any]]:
        """Get articles that need processing."""
        # Get date range
        start_dt, end_dt = self.db.article_service.get_article_date_range()
        if start_date:
            start_dt = max(start_dt, datetime.strptime(
                start_date, "%Y-%m-%d").replace(tzinfo=TZ))
        if end_date:
            end_dt = min(end_dt, datetime.strptime(
                end_date, "%Y-%m-%d").replace(tzinfo=TZ))

        # Get target dates
        target_dates = get_target_dates(
            start_dt, end_dt) if filter_target_dates else None

        if target_dates:
            logger.info(f"Filter articles on {len(target_dates)} target dates")

        articles = self.db.article_service.get_articles_for_llm_batch(
            # Exclude already succeeded articles with the api and prompt type
            prompt_type,
            llm_excluded_statuses=[CompletionStatus.SUCCEEDED.value,
                                   CompletionStatus.IN_PROGRESS.value],
            date_list=target_dates,
            start_date=start_dt,
            end_date=end_dt,
            limit=max_articles,
            # Filter short articles
            min_words=self.llm_config.min_input
        )
        if start_dt and end_dt:
            logger.info(
                f"Fetched {len(articles)} articles between {start_dt.strftime('%Y-%m-%d')} and {end_dt.strftime('%Y-%m-%d')}")
        else:
            logger.info(f"Fetched {len(articles)} articles")

        return articles

    def save_completion(self, result_dict: Dict[str, Any], api_name: str, prompt_type: str, batch_id: str) -> Optional[Dict[str, Any]]:
        """Save completion result to database."""
        try:
            article_id = extract_article_id(result_dict['custom_id'])
            db_record = {
                "id": result_dict['custom_id'],
                'article_id': article_id,
                "api": api_name,
                "prompt_type": prompt_type,
                "model": result_dict['model'],
                "status": result_dict['status'],
                "batch_id": batch_id,
                "cost": result_dict.get('cost') or result_dict.get('estimated_cost'),
                "raw_response": result_dict.get('raw_response')
            }
            if 'user_prompt' in result_dict and 'system_prompt' in result_dict:
                db_record['prompt'] = result_dict['user_prompt'] + \
                    result_dict['system_prompt']

            if 'content' in result_dict:
                parsed_result = self.prompt_manager.parse_result(
                    result_dict['content'], prompt_type)
                db_record['content'] = parsed_result

                # Save to article metadata
                if self.db.article_service.add_article_metadata(
                        article_id, {prompt_type: db_record['content']}):
                    logger.debug(
                        f"Successfully saved in article metadata: {article_id} ")
                else:
                    logger.error(
                        f"Failed to save in article metadata: {article_id}")

            if self.db.llm_api_service.upsert_llm_result(db_record):
                logger.debug(
                    f"Successfully saved result: {result_dict['custom_id']}")
            else:
                logger.error(
                    f"Failed to save result: {result_dict['custom_id']}")

            return db_record
        except Exception as e:
            logger.error(f"Error saving result: {e}")

        return None

    def save_batch(self, batch_dict: Dict[str, Any], api_name: str, prompt_type: str) -> Dict[str, Any]:
        """Save batch request information to database."""
        try:
            db_record = {
                'id': batch_dict['id'],
                'api': api_name,
                'status': batch_dict['status'],
                'created_at': batch_dict['created_at'],
                'completed_at': batch_dict.get('completed_at'),
                'expires_at': batch_dict['expires_at'],
                'prompt_type': prompt_type,
                'raw_response': batch_dict['raw_response'],
                'cost': batch_dict.get('cost')
            }
            self.db.llm_api_service.upsert_llm_batch(db_record)

            logger.info(f"Successfully saved batch: {batch_dict['id']}")
            return db_record

        except Exception as e:
            logger.error(f"Error saving batch: {e}")
            return {}

    def stop_processing(self):
        """Stop all processing gracefully."""
        self.running = False
        self.shutdown_event.set()
        logger.info("Batch processing stop requested")

    # ============================================================================
    # COMPONENT 1: BATCH CREATOR
    # ============================================================================

    async def batch_creator_component(
        self,
        prompt_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        filter_target_dates: bool = False
    ) -> None:
        """
        Component 1: Batch Creator

        Continuously reads articles from database, groups them into batches,
        and puts them into the shared queue for processing.
        """
        logger.info(f"Starting Batch Creator component for {prompt_type}")

        try:
            while self.running and not self.shutdown_event.is_set():
                try:
                    # Get articles that need processing
                    articles = self.get_article_candidates(
                        prompt_type=prompt_type,
                        start_date=start_date,
                        end_date=end_date,
                        max_articles=self.async_config.max_articles_per_cycle,
                        filter_target_dates=filter_target_dates
                    )

                    if articles:
                        logger.info(
                            f"Batch Creator: Found {len(articles)} articles to process")

                        # Split articles into batches
                        batch_count = 0
                        for i in range(0, len(articles), self.async_config.batch_size):
                            if not self.running or self.shutdown_event.is_set():
                                break

                            article_batch = articles[i:i +
                                                     self.async_config.batch_size]
                            batch_id = f"batch_{prompt_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{i//self.async_config.batch_size}"

                            batch_item = BatchItem(
                                articles=article_batch,
                                prompt_type=prompt_type,
                                batch_id=batch_id
                            )

                            # Put batch in queue (with timeout to avoid blocking)
                            try:
                                await asyncio.wait_for(
                                    self.batch_queue.put(batch_item),
                                    timeout=self.async_config.api_request_timeout
                                )
                                batch_count += 1
                                logger.debug(
                                    f"Batch Creator: Queued batch {batch_id} with {len(article_batch)} articles")

                            except asyncio.TimeoutError:
                                logger.warning(
                                    f"Batch Creator: Queue full, skipping batch {batch_id}")
                                break

                        logger.info(
                            f"Batch Creator: Successfully queued {batch_count} batches")
                        self.stats['batches_created'] += batch_count

                    else:
                        logger.debug(
                            f"Batch Creator: No new articles found for {prompt_type}")

                    # Wait before next cycle
                    await asyncio.sleep(self.async_config.batch_creation_interval)

                except Exception as e:
                    logger.error(
                        f"Batch Creator: Error in processing cycle: {e}")
                    await asyncio.sleep(min(self.async_config.batch_creation_interval, 60))

        except asyncio.CancelledError:
            logger.info("Batch Creator: Component cancelled")
            raise
        except Exception as e:
            logger.error(f"Batch Creator: Fatal error: {e}")
            raise
        finally:
            logger.info("Batch Creator: Component stopped")

    # ============================================================================
    # COMPONENT 2 & 3: API PROCESSORS (OpenAI and Anthropic)
    # ============================================================================

    async def _create_batch_request(self, api_name: str, batch_item: BatchItem) -> Optional[Any]:
        """Create a batch request for the specified API."""
        try:
            llm_api = self.llm_apis[api_name]
            requests = []
            prompt = self.prompt_manager.get_prompt(batch_item.prompt_type)
            system_prompt = prompt['system_prompt']
            model = API_MODELS[api_name]

            for article in batch_item.articles:
                article_input = process_article(
                    article, self.llm_config.min_input, self.llm_config.max_input)
                if not article_input:
                    continue

                rid = generate_custom_id(
                    article['id'], batch_item.prompt_type, api_name)

                formatted_prompt = prompt['prompt_template'].format(
                    title=article_input['title'],
                    content=article_input['content']
                )

                request = CompletionRequest(
                    max_tokens=self.llm_config.max_tokens,
                    temperature=self.llm_config.temperature,
                    user_prompt=formatted_prompt,
                    system_prompt=system_prompt,
                    model=model,
                    custom_id=rid
                )
                requests.append(request)

            if not requests:
                logger.warning(
                    f"{api_name} Processor: No valid requests in batch {batch_item.batch_id}")
                return None

            # Create batch with timeout
            batch = await asyncio.wait_for(
                asyncio.to_thread(llm_api.get_completion_batch, requests),
                timeout=self.async_config.batch_creation_timeout
            )

            if not batch:
                return None

            # Wait for batch validation (OpenAI specific)
            if batch.status == BatchStatus.VALIDATING.value:
                logger.info(
                    f"{api_name} Processor: Batch {batch.id} is validating...")
                validation_timeout = 60  # 1 minute timeout for validation
                start_time = asyncio.get_event_loop().time()

                while (batch and batch.status == BatchStatus.VALIDATING.value and
                       (asyncio.get_event_loop().time() - start_time) < validation_timeout):
                    await asyncio.sleep(2)
                    batch = await asyncio.to_thread(
                        llm_api.retrieve_batch, batch.id, fetch_results=False)

            if batch and batch.status == BatchStatus.FAILED.value:
                logger.error(
                    f"{api_name} Processor: Batch {batch.id} failed validation")
                return None

            # Save batch and requests to database
            if batch:
                self.save_batch(batch.to_dict(), api_name,
                                batch_item.prompt_type)
                self.active_batches[api_name].add(batch.id)

                for request in requests:
                    request_dict = request.to_dict()
                    request_dict['status'] = CompletionStatus.IN_PROGRESS.value
                    self.save_completion(
                        request_dict, api_name, batch_item.prompt_type, batch_id=batch.id)

            return batch

        except asyncio.TimeoutError:
            logger.error(
                f"{api_name} Processor: Timeout creating batch {batch_item.batch_id}")
            return None
        except Exception as e:
            logger.error(
                f"{api_name} Processor: Error creating batch {batch_item.batch_id}: {e}")
            return None

    async def openai_processor_component(self) -> None:
        """
        Component 2: OpenAI API Processor (High Priority)

        Takes batches from queue with priority access and submits to OpenAI API.
        Implements exponential backoff on failures.
        """
        logger.info("Starting OpenAI Processor component (High Priority)")

        try:
            while self.running and not self.shutdown_event.is_set():
                try:
                    # Check if we're at the concurrent batch limit
                    if len(self.active_batches['openai']) >= self.async_config.max_concurrent_batches:
                        logger.debug(
                            "OpenAI Processor: At concurrent batch limit, waiting...")
                        await asyncio.sleep(self.async_config.openai_priority_delay)
                        continue

                    # Get batch from queue with priority delay (shorter delay = higher priority)
                    try:
                        batch_item = await asyncio.wait_for(
                            self.batch_queue.get(),
                            timeout=self.async_config.openai_priority_delay
                        )
                    except asyncio.TimeoutError:
                        # No batch available, continue loop
                        continue

                    logger.info(
                        f"OpenAI Processor: Processing batch {batch_item.batch_id} with {len(batch_item)} articles")

                    # Attempt to create batch with retry logic
                    batch = None
                    for attempt in range(self.async_config.max_retries):
                        try:
                            batch = await self._create_batch_request('openai', batch_item)
                            if batch:
                                logger.info(
                                    f"OpenAI Processor: Successfully created batch {batch.id}")
                                self.stats['articles_processed'] += len(
                                    batch_item)
                                break
                            else:
                                logger.warning(
                                    f"OpenAI Processor: Failed to create batch (attempt {attempt + 1})")

                        except Exception as e:
                            logger.error(
                                f"OpenAI Processor: Error on attempt {attempt + 1}: {e}")

                        # Exponential backoff before retry
                        if attempt < self.async_config.max_retries - 1:
                            delay = await self._calculate_backoff_delay(attempt)
                            logger.info(
                                f"OpenAI Processor: Retrying in {delay:.1f}s...")
                            await asyncio.sleep(delay)

                    if not batch:
                        logger.error(
                            f"OpenAI Processor: Failed to create batch {batch_item.batch_id} after all retries")
                        self.stats['batches_failed'] += 1

                    # Mark task as done
                    self.batch_queue.task_done()

                except Exception as e:
                    logger.error(
                        f"OpenAI Processor: Error processing batch: {e}")
                    await asyncio.sleep(self.async_config.openai_priority_delay)

        except asyncio.CancelledError:
            logger.info("OpenAI Processor: Component cancelled")
            raise
        except Exception as e:
            logger.error(f"OpenAI Processor: Fatal error: {e}")
            raise
        finally:
            logger.info("OpenAI Processor: Component stopped")

    async def anthropic_processor_component(self) -> None:
        """
        Component 3: Anthropic API Processor (Lower Priority)

        Takes remaining batches from queue and submits to Anthropic API.
        Implements exponential backoff on failures.
        """
        logger.info("Starting Anthropic Processor component (Lower Priority)")

        try:
            while self.running and not self.shutdown_event.is_set():
                try:
                    # Check if we're at the concurrent batch limit
                    if len(self.active_batches['anthropic']) >= self.async_config.max_concurrent_batches:
                        logger.debug(
                            "Anthropic Processor: At concurrent batch limit, waiting...")
                        await asyncio.sleep(self.async_config.anthropic_delay)
                        continue

                    # Wait longer than OpenAI to give it priority
                    await asyncio.sleep(self.async_config.anthropic_delay)

                    # Get batch from queue
                    try:
                        batch_item = await asyncio.wait_for(
                            self.batch_queue.get(),
                            timeout=self.async_config.anthropic_delay
                        )
                    except asyncio.TimeoutError:
                        # No batch available, continue loop
                        continue

                    logger.info(
                        f"Anthropic Processor: Processing batch {batch_item.batch_id} with {len(batch_item)} articles")

                    # Attempt to create batch with retry logic
                    batch = None
                    for attempt in range(self.async_config.max_retries):
                        try:
                            batch = await self._create_batch_request('anthropic', batch_item)
                            if batch:
                                logger.info(
                                    f"Anthropic Processor: Successfully created batch {batch.id}")
                                self.stats['articles_processed'] += len(
                                    batch_item)
                                break
                            else:
                                logger.warning(
                                    f"Anthropic Processor: Failed to create batch (attempt {attempt + 1})")

                        except Exception as e:
                            logger.error(
                                f"Anthropic Processor: Error on attempt {attempt + 1}: {e}")

                        # Exponential backoff before retry
                        if attempt < self.async_config.max_retries - 1:
                            delay = await self._calculate_backoff_delay(attempt)
                            logger.info(
                                f"Anthropic Processor: Retrying in {delay:.1f}s...")
                            await asyncio.sleep(delay)

                    if not batch:
                        logger.error(
                            f"Anthropic Processor: Failed to create batch {batch_item.batch_id} after all retries")
                        self.stats['batches_failed'] += 1

                    # Mark task as done
                    self.batch_queue.task_done()

                except Exception as e:
                    logger.error(
                        f"Anthropic Processor: Error processing batch: {e}")
                    await asyncio.sleep(self.async_config.anthropic_delay)

        except asyncio.CancelledError:
            logger.info("Anthropic Processor: Component cancelled")
            raise
        except Exception as e:
            logger.error(f"Anthropic Processor: Fatal error: {e}")
            raise
        finally:
            logger.info("Anthropic Processor: Component stopped")

    # ============================================================================
    # COMPONENT 4: BATCH MONITOR
    # ============================================================================

    async def _process_single_batch(self, batch_record: Dict[str, Any], prompt_type: str) -> None:
        """Process a single batch and handle results."""
        try:
            api_name = batch_record['api']
            batch_id = batch_record['id']
            llm_api = self.llm_apis.get(api_name)

            if not llm_api:
                logger.error(f"Batch Monitor: Unknown API: {api_name}")
                return

            # Retrieve batch status and results
            batch = await asyncio.to_thread(
                llm_api.retrieve_batch, batch_id, fetch_results=True)
            if not batch:
                logger.error(
                    f"Batch Monitor: Failed to retrieve batch {batch_id}")
                return

            logger.debug(
                f"Batch Monitor: Retrieved batch {batch.id} with status {batch.status}")

            if batch.status in COMPLETED_BATCH_STATUSES:
                logger.info(
                    f"Batch Monitor: Batch {batch.id} completed. Processing {len(batch.completion_results)} results...")

                # Process completed results
                for result in batch.completion_results:
                    self.save_completion(
                        result.to_dict(), api_name, prompt_type, batch_id=batch.id)

                # Mark batch as processed
                batch.status = BatchStatus.PROCESSED.value
                self.stats['batches_completed'] += 1

                # Remove from active tracking
                self.active_batches[api_name].discard(batch_id)

            elif batch.status in INCOMPLETED_BATCH_STATUSES:
                logger.error(
                    f"Batch Monitor: Batch {batch.id} failed with status {batch.status}")

                # Mark all requests as failed
                requests = self.db.llm_api_service.get_llm_results(
                    batch_id=batch.id)
                logger.info(
                    f"Batch Monitor: Marking {len(requests)} requests as failed...")
                for request in requests:
                    request['status'] = CompletionStatus.FAILED.value
                    self.save_completion(
                        request, api_name, prompt_type, batch_id=batch.id)

                self.stats['batches_failed'] += 1

                # Remove from active tracking
                self.active_batches[api_name].discard(batch_id)

            elif batch.status in ACTIVE_BATCH_STATUSES:
                logger.debug(
                    f"Batch Monitor: Batch {batch.id} is still active")
            else:
                logger.warning(
                    f"Batch Monitor: Unknown batch status {batch.status} for batch {batch.id}")

            # Update batch status in database
            self.save_batch(batch.to_dict(), api_name, prompt_type)

        except Exception as e:
            logger.error(
                f"Batch Monitor: Error processing batch {batch_record.get('id', 'unknown')}: {e}")

    async def batch_monitor_component(self, prompt_type: str) -> None:
        """
        Component 4: Batch Monitor

        Continuously polls all active batch requests across both APIs,
        checks batch status, and processes completed results.
        """
        logger.info(f"Starting Batch Monitor component for {prompt_type}")

        try:
            while self.running and not self.shutdown_event.is_set():
                try:
                    # Get active batches from database
                    active_batches = self.db.llm_api_service.get_llm_batch_status(
                        prompt_type=prompt_type,
                        included_status=ACTIVE_BATCH_STATUSES
                    )

                    if active_batches:
                        logger.info(
                            f"Batch Monitor: Found {len(active_batches)} active batches to check")

                        # Process batches concurrently but with limited concurrency
                        # Limit concurrent batch processing
                        semaphore = asyncio.Semaphore(5)

                        async def process_with_semaphore(batch_record):
                            async with semaphore:
                                await self._process_single_batch(batch_record, prompt_type)
                                # Small delay between batch processing
                                await asyncio.sleep(0.5)

                        # Process all active batches concurrently
                        await asyncio.gather(
                            *[process_with_semaphore(batch_record)
                              for batch_record in active_batches],
                            return_exceptions=True
                        )

                    else:
                        logger.debug(
                            f"Batch Monitor: No active batches found for {prompt_type}")

                    # Wait for next polling cycle
                    logger.debug(
                        f"Batch Monitor: Sleeping for {self.async_config.batch_monitoring_interval}s...")
                    await asyncio.sleep(self.async_config.batch_monitoring_interval)

                except Exception as e:
                    logger.error(
                        f"Batch Monitor: Error in monitoring cycle: {e}")
                    # Continue monitoring even if there's an error, but wait a bit longer
                    await asyncio.sleep(min(self.async_config.batch_monitoring_interval, 60))

        except asyncio.CancelledError:
            logger.info("Batch Monitor: Component cancelled")
            raise
        except Exception as e:
            logger.error(f"Batch Monitor: Fatal error: {e}")
            raise
        finally:
            logger.info("Batch Monitor: Component stopped")

    # ============================================================================
    # MAIN ORCHESTRATION METHODS
    # ============================================================================

    async def run_async_batch_processing(
        self,
        prompt_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        filter_target_dates: bool = False
    ) -> None:
        """
        Main method to run all four async components concurrently.

        This orchestrates the entire async batch processing pipeline:
        1. Batch Creator - reads articles and creates batches
        2. OpenAI Processor - processes batches with high priority
        3. Anthropic Processor - processes remaining batches
        4. Batch Monitor - monitors and processes completed batches
        """
        logger.info(
            f"Starting async batch processing pipeline for {prompt_type}")
        logger.info(f"Configuration: {self.async_config}")

        try:
            # Create all four async tasks
            tasks = [
                asyncio.create_task(
                    self.batch_creator_component(
                        prompt_type=prompt_type,
                        start_date=start_date,
                        end_date=end_date,
                        filter_target_dates=filter_target_dates
                    ),
                    name="BatchCreator"
                ),
                asyncio.create_task(
                    self.openai_processor_component(),
                    name="OpenAIProcessor"
                ),
                asyncio.create_task(
                    self.anthropic_processor_component(),
                    name="AnthropicProcessor"
                ),
                asyncio.create_task(
                    self.batch_monitor_component(prompt_type),
                    name="BatchMonitor"
                )
            ]

            logger.info("All components started, running concurrently...")

            # Run all components concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Check for exceptions in any component
            for i, result in enumerate(results):
                task_name = tasks[i].get_name()
                if isinstance(result, Exception):
                    logger.error(f"Exception in {task_name}: {result}")
                else:
                    logger.info(f"{task_name} completed successfully")

        except asyncio.CancelledError:
            logger.info("Async batch processing cancelled")
            # Cancel all tasks
            for task in tasks:
                if not task.done():
                    task.cancel()
            # Wait for all tasks to complete cancellation
            await asyncio.gather(*tasks, return_exceptions=True)
            raise
        except Exception as e:
            logger.error(f"Fatal error in async batch processing: {e}")
            # Cancel all tasks
            for task in tasks:
                if not task.done():
                    task.cancel()
            raise
        finally:
            # Print final statistics
            logger.info("=== Final Processing Statistics ===")
            logger.info(f"Batches created: {self.stats['batches_created']}")
            logger.info(
                f"Batches completed: {self.stats['batches_completed']}")
            logger.info(f"Batches failed: {self.stats['batches_failed']}")
            logger.info(
                f"Articles processed: {self.stats['articles_processed']}")
            logger.info("Async batch processing pipeline stopped")

    @asynccontextmanager
    async def async_context(self):
        """Async context manager for proper resource cleanup."""
        try:
            logger.info("LLMBatchAnalyzerV2: Async context started")
            yield self
        finally:
            # Cleanup resources
            self.stop_processing()

            # Wait for queue to be empty
            if not self.batch_queue.empty():
                logger.info("Waiting for batch queue to empty...")
                await self.batch_queue.join()

            logger.info("LLMBatchAnalyzerV2: Resources cleaned up")


# ============================================================================
# COMMAND LINE INTERFACE
# ============================================================================

async def main():
    """Main function to run the v2 analyzer from command line."""
    parser = argparse.ArgumentParser(
        description='LLM Batch Analyzer V2 - Advanced async batch processing')

    parser.add_argument('-t', '--prompt-type',
                        default='influence', help='Type of analysis to perform')

    # Date filtering
    parser.add_argument('-s', '--start-date', help='Start date (YYYY-MM-DD)')
    parser.add_argument('-e', '--end-date', help='End date (YYYY-MM-DD)')
    parser.add_argument('--filter-target-dates', action='store_true',
                        help='Filter dates based on SPY price movements')

    # Resource limits
    parser.add_argument('-b', '--budget', type=float, default=0.1,
                        help='Maximum budget for API calls')
    parser.add_argument('--max-input', type=int, default=2048,
                        help='Maximum input words')
    parser.add_argument('--max-tokens', type=int, default=512,
                        help='Maximum output tokens')
    parser.add_argument('--min-input', type=int, default=200,
                        help='Minimum input words')
    parser.add_argument('--requests-per-minute', type=int, default=10,
                        help='Rate limit per minute')

    # Batch processing configuration
    parser.add_argument('--batch-size', type=int, default=20,
                        help='Batch size for processing')
    parser.add_argument('--max-articles-per-cycle', type=int, default=1000,
                        help='Maximum articles to process per cycle')
    parser.add_argument('--queue-maxsize', type=int, default=100,
                        help='Maximum queue size for batches')

    # Timing configuration
    parser.add_argument('--batch-creation-interval', type=int, default=60,
                        help='Interval between batch creation cycles (seconds)')
    parser.add_argument('--batch-monitoring-interval', type=int, default=300,
                        help='Interval between batch monitoring cycles (seconds)')

    # Retry and backoff configuration
    parser.add_argument('--max-retries', type=int, default=3,
                        help='Maximum retries for failed operations')
    parser.add_argument('--initial-backoff-delay', type=float, default=1.0,
                        help='Initial backoff delay in seconds')
    parser.add_argument('--max-backoff-delay', type=float, default=300.0,
                        help='Maximum backoff delay in seconds')
    parser.add_argument('--backoff-multiplier', type=float, default=2.0,
                        help='Exponential backoff multiplier')

    # API priority configuration
    parser.add_argument('--openai-priority-delay', type=float, default=0.1,
                        help='Delay for OpenAI priority access (seconds)')
    parser.add_argument('--anthropic-delay', type=float, default=1.0,
                        help='Additional delay for Anthropic processor (seconds)')

    # Timeout configuration
    parser.add_argument('--api-request-timeout', type=float, default=30.0,
                        help='Timeout for individual API requests (seconds)')
    parser.add_argument('--batch-creation-timeout', type=float, default=120.0,
                        help='Timeout for batch creation (seconds)')

    # Resource limits
    parser.add_argument('--max-concurrent-batches', type=int, default=50,
                        help='Maximum concurrent batches per API')

    args = parser.parse_args()

    try:
        # Create configurations
        llm_config = LLMConfig(
            max_tokens=args.max_tokens,
            max_input=args.max_input,
            min_input=args.min_input,
            temperature=0.7,
            requests_per_minute=args.requests_per_minute
        )

        async_config = AsyncBatchConfig(
            batch_size=args.batch_size,
            max_articles_per_cycle=args.max_articles_per_cycle,
            queue_maxsize=args.queue_maxsize,
            batch_creation_interval=args.batch_creation_interval,
            batch_monitoring_interval=args.batch_monitoring_interval,
            max_retries=args.max_retries,
            initial_backoff_delay=args.initial_backoff_delay,
            max_backoff_delay=args.max_backoff_delay,
            backoff_multiplier=args.backoff_multiplier,
            openai_priority_delay=args.openai_priority_delay,
            anthropic_delay=args.anthropic_delay,
            api_request_timeout=args.api_request_timeout,
            batch_creation_timeout=args.batch_creation_timeout,
            max_concurrent_batches=args.max_concurrent_batches
        )

        # Initialize database and analyzer
        db_manager = DatabaseManager()
        batch_analyzer = LLMBatchAnalyzerV2(
            llm_config, async_config, db_manager)

        # Check budget
        current_total_cost = db_manager.llm_api_service.get_total_cost(
            prompt_type=args.prompt_type)
        if current_total_cost >= args.budget:
            logger.warning(f"Current cost {current_total_cost} exceeds budget limit "
                           f"{args.budget} for prompt type {args.prompt_type}.")
        BaseAPIManager.set_budget(args.budget, current_total_cost)

        # Run the async batch processing pipeline
        async with batch_analyzer.async_context():
            try:
                logger.info("=== LLM Batch Analyzer V2 Starting ===")
                logger.info(f"Prompt type: {args.prompt_type}")
                logger.info(f"Budget: ${args.budget}")
                logger.info(f"LLM Config: {llm_config}")
                logger.info(f"Async Config: {async_config}")
                logger.info("Starting async batch processing pipeline...")

                await batch_analyzer.run_async_batch_processing(
                    prompt_type=args.prompt_type,
                    start_date=args.start_date,
                    end_date=args.end_date,
                    filter_target_dates=args.filter_target_dates
                )

            except KeyboardInterrupt:
                logger.info(
                    "Received interrupt signal, stopping processing...")
                batch_analyzer.stop_processing()
            except Exception as e:
                logger.error(f"Unexpected error during batch processing: {e}")
                batch_analyzer.stop_processing()
                raise

    except Exception as e:
        logger.error(f"Application error: {e}")
        raise


if __name__ == '__main__':
    asyncio.run(main())
