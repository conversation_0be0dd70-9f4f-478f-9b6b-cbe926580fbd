import argparse
import async<PERSON>
from datetime import date, datetime, timedelta
from typing import Any, Dict, List, Optional, Set
from contextlib import asynccontextmanager
from apis.llm.anthropic import AnthropicManager
from apis.llm.base import BaseAPIManager
from apis.llm.data_types import ACTIVE_BATCH_STATUSES, COMPLETED_BATCH_STATUSES, INCOMPLETED_BATCH_STATUSES, BatchStatus, CompletionRequest, CompletionStatus
from apis.llm.openai import OpenAIManager
from apis.yahoo_finance import yahoo_api

from db.database import DatabaseManager
from nlp.llm.prompt import PromptManager
from nlp.llm.helpers import TZ, LLMConfig, extract_article_id, generate_custom_id, process_article

# Configure logging
from utils.logging_config import configure_logging
logger = configure_logging(__name__, log_file='llm_analyzer.log')

API_MODELS = {
    'openai': 'gpt-4.1-nano',
    'anthropic': 'claude-3-5-haiku'
}

# Async configuration constants
DEFAULT_BATCH_POLLING_INTERVAL = 300  # 5 minutes for batch status polling


def get_target_dates(
    start_date: datetime,
    end_date: datetime,
    threshold: float = 0.01,
    interval: str = "1d",
    days_before: int = 3,
    days_after: int = 3,
) -> Set[date]:
    """
    Fetch dates where SPY's absolute daily return exceeded the given threshold.

    Args:
        start_date: Start date for analysis.
        end_date: End date for analysis.
        threshold: Minimum absolute daily return (e.g., 0.01 for 1%).
        interval: Data interval, default is "1d".
        days_before: How many days before the target date to include.
        days_after: How many days after the target date to include.

    Returns:
        Set of target dates (datetime.date).
    """
    try:
        df = yahoo_api.get_price_data(
            ticker="SPY",
            start_date=start_date,
            end_date=end_date,
            interval=interval
        )
    except Exception as e:
        logger.error(f"Error fetching price data: {e}")
        return set()

    if df.empty or "Close" not in df.columns:
        logger.warning("No price data available for the specified period")
        return set()

    df["return"] = df["Close"].pct_change()
    mask = df["return"].abs() > threshold
    filtered = df[mask]

    result = set()
    for ts in filtered.index:
        base_date = ts.replace(tzinfo=TZ).date()
        for offset in range(-days_before, days_after + 1):
            result.add(base_date + timedelta(days=offset))

    return result


class LLMBatchAnalyzer:
    """Handles batch processing of articles with async operations."""

    def __init__(self, config: LLMConfig, db: DatabaseManager):
        self.config = config
        self.db = db
        self.prompt_manager = PromptManager()
        self.llm_apis = self._initialize_llm_apis()
        self.running = True  # Flag to control polling loop

    def _initialize_llm_apis(self) -> Dict[str, BaseAPIManager]:
        """Initialize the LLM APIs."""
        api_mapping = {
            'openai': OpenAIManager,
            'anthropic': AnthropicManager
        }

        llm_apis = {}
        for api_name, api_class in api_mapping.items():
            llm_apis[api_name] = api_class(
                requests_per_minute=self.config.requests_per_minute
            )
        return llm_apis

    def save_completion(self, result_dict: Dict[str, Any], api_name: str, prompt_type: str, batch_id: str) -> Optional[Dict[str, Any]]:
        """Save completion result to database."""
        try:
            article_id = extract_article_id(result_dict['custom_id'])
            db_record = {
                "id": result_dict['custom_id'],
                'article_id': article_id,
                "api": api_name,
                "prompt_type": prompt_type,
                "model": result_dict['model'],
                "status": result_dict['status'],
                "batch_id": batch_id,
                "cost": result_dict.get('cost') or result_dict.get('estimated_cost'),
                "raw_response": result_dict.get('raw_response')
            }
            if 'user_prompt' in result_dict and 'system_prompt' in result_dict:
                db_record['prompt'] = result_dict['user_prompt'] + \
                    result_dict['system_prompt']

            if 'content' in result_dict:
                parsed_result = self.prompt_manager.parse_result(
                    result_dict['content'], prompt_type)
                db_record['content'] = parsed_result

                # Save to article metadata
                if self.db.article_service.add_article_metadata(
                        article_id, {prompt_type: db_record['content']}):
                    logger.debug(
                        f"Successfully saved in article metadata: {article_id} ")
                else:
                    logger.error(
                        f"Failed to save in article metadata: {article_id}")

            if self.db.llm_api_service.upsert_llm_result(db_record):
                logger.debug(
                    f"Successfully saved result: {result_dict['custom_id']}")
            else:
                logger.error(
                    f"Failed to save result: {result_dict['custom_id']}")

            return db_record
        except Exception as e:
            logger.error(f"Error saving result: {e}")

        return None

    def save_batch(self, batch_dict: Dict[str, Any], api_name: str, prompt_type: str) -> Dict[str, Any]:
        """Save batch request information to database."""
        try:
            db_record = {
                'id': batch_dict['id'],
                'api': api_name,
                'status': batch_dict['status'],
                'created_at': batch_dict['created_at'],
                'completed_at': batch_dict.get('completed_at'),
                'expires_at': batch_dict['expires_at'],
                'prompt_type': prompt_type,
                'raw_response': batch_dict['raw_response'],
                'cost': batch_dict.get('cost')
            }
            self.db.llm_api_service.upsert_llm_batch(db_record)

            logger.info(f"Successfully saved batch: {batch_dict['id']}")
            return db_record

        except Exception as e:
            logger.error(f"Error saving batch: {e}")
            return {}

    async def create_batch_request(self, api_name: str, prompt_type: str, model: str, target_articles: List[Dict[str, Any]]):
        """Process articles in batch using sync APIs."""
        try:
            llm_api = self.llm_apis[api_name]
            requests = []
            prompt = self.prompt_manager.get_prompt(prompt_type)
            system_prompt = prompt['system_prompt']

            for article in target_articles:
                article_input = process_article(
                    article, self.config.min_input, self.config.max_input)
                if not article_input:
                    continue

                rid = generate_custom_id(
                    article['id'], prompt_type, api_name)

                formatted_prompt = prompt['prompt_template'].format(
                    title=article_input['title'],
                    content=article_input['content']
                )

                request = CompletionRequest(
                    max_tokens=self.config.max_tokens,
                    temperature=self.config.temperature,
                    user_prompt=formatted_prompt,
                    system_prompt=system_prompt,
                    model=model,
                    custom_id=rid
                )
                requests.append(request)

            batch = llm_api.get_completion_batch(requests)
            if not batch:
                return None

            # Wait for batch to be validated (openai) with sync polling
            if batch.status == BatchStatus.VALIDATING.value:
                logger.info(
                    f"Batch {batch.id} is validating. Waiting for completion...")
                while batch and batch.status == BatchStatus.VALIDATING.value:
                    await asyncio.sleep(2)
                    batch = llm_api.retrieve_batch(
                        batch.id, fetch_results=False)

            if batch.status == BatchStatus.FAILED.value:
                logger.error(f"Batch {batch.id} failed")
                return None

            self.save_batch(batch.to_dict(), api_name, prompt_type)

            for request in requests:
                request_dict = request.to_dict()
                request_dict['status'] = CompletionStatus.IN_PROGRESS.value
                self.save_completion(
                    request_dict, api_name, prompt_type, batch_id=batch.id)

            return batch

        except Exception as e:
            logger.error(f"Error processing batch: {e}")
            return None

    def get_article_candidates(
        self,
        prompt_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        max_articles: Optional[int] = None,
        filter_target_dates: bool = False
    ) -> List[Dict[str, Any]]:
        # Get date range
        start_dt, end_dt = self.db.article_service.get_article_date_range()
        if start_date:
            start_dt = max(start_dt, datetime.strptime(
                start_date, "%Y-%m-%d").replace(tzinfo=TZ))
        if end_date:
            end_dt = min(end_dt, datetime.strptime(
                end_date, "%Y-%m-%d").replace(tzinfo=TZ))

        # Get target dates
        target_dates = get_target_dates(
            start_dt, end_dt) if filter_target_dates else None

        if target_dates:
            logger.info(f"Filter articles on {len(target_dates)} target dates")

        articles = self.db.article_service.get_articles_for_llm_batch(
            # Exclude already succeeded articles with the api and prompt type
            prompt_type,
            llm_excluded_statuses=[CompletionStatus.SUCCEEDED.value,
                                   CompletionStatus.IN_PROGRESS.value],
            date_list=target_dates,
            start_date=start_dt,
            end_date=end_dt,
            limit=max_articles,
            # Filter short articles
            min_words=self.config.min_input
        )
        if start_dt and end_dt:
            logger.info(
                f"Fetched {len(articles)} articles between {start_dt.strftime('%Y-%m-%d')} and {end_dt.strftime('%Y-%m-%d')}")
        else:
            logger.info(f"Fetched {len(articles)} articles")

        return articles

    async def _process_single_batch(self, batch_record: Dict[str, Any], prompt_type: str) -> None:
        """Process a single batch using sync APIs."""
        try:
            api_name = batch_record['api']
            llm_api = self.llm_apis.get(api_name)

            if not llm_api:
                logger.error(f"Unknown API: {api_name}")
                return

            batch = llm_api.retrieve_batch(
                batch_record['id'], fetch_results=True)
            if not batch:
                logger.error(f"Failed to retrieve batch {batch_record['id']}")
                return

            logger.info(
                f"Retrieved batch {batch.id} with status {batch.status}")

            if batch.status in COMPLETED_BATCH_STATUSES:
                logger.info(
                    f"Batch {batch.id} is completed. Saving {len(batch.completion_results)} results...")
                for result in batch.completion_results:
                    self.save_completion(
                        result.to_dict(), api_name, prompt_type, batch_id=batch.id)
                batch.status = BatchStatus.PROCESSED.value

            elif batch.status in INCOMPLETED_BATCH_STATUSES:
                logger.error(f"Batch {batch.id} failed. Status {batch.status}")
                requests = self.db.llm_api_service.get_llm_results(
                    batch_id=batch.id)
                logger.info(f"Marking {len(requests)} requests as failed...")
                for request in requests:
                    request['status'] = CompletionStatus.FAILED.value
                    self.save_completion(
                        request, api_name, prompt_type, batch_id=batch.id)

            elif batch.status in ACTIVE_BATCH_STATUSES:
                logger.info(f"Batch {batch.id} is still active")
            else:
                logger.error(f"Invalid batch status {batch.status}")

            self.save_batch(batch.to_dict(), api_name, prompt_type)

        except Exception as e:
            logger.error(
                f"Error processing batch {batch_record.get('id', 'unknown')}: {e}")

    async def retrieve_and_process_batches(self, prompt_type: str, polling_interval: int = DEFAULT_BATCH_POLLING_INTERVAL) -> None:
        """
        Periodic polling service to monitor and process existing batches.

        This method runs continuously, checking for active batches at regular intervals,
        retrieving their status from the API, and processing completed/failed batches.
        """
        logger.info(
            f"Starting batch polling service for {prompt_type} (interval: {polling_interval}s)")

        while self.running:
            try:
                # Get active batches from database
                active_batches = self.db.llm_api_service.get_llm_batch_status(
                    prompt_type=prompt_type,
                    included_status=ACTIVE_BATCH_STATUSES
                )

                if active_batches:
                    logger.info(
                        f"Found {len(active_batches)} active batches to process")

                    # Process each batch sequentially to avoid overwhelming APIs
                    for batch_record in active_batches:
                        await self._process_single_batch(batch_record, prompt_type)
                        # Small delay between batch processing to be gentle on APIs
                        await asyncio.sleep(1)

                else:
                    logger.info(f"No active batches found for {prompt_type}")

                # Wait for next polling cycle
                logger.info(
                    f"Batch polling cycle complete. Sleeping for {polling_interval}s...")
                await asyncio.sleep(polling_interval)

            except Exception as e:
                logger.error(f"Error in batch polling cycle: {e}")
                # Continue polling even if there's an error, but wait a bit longer
                await asyncio.sleep(min(polling_interval, 60))

        logger.info(f"Batch polling service stopped for {prompt_type}")

    def stop_polling(self):
        """Stop the polling service gracefully."""
        self.running = False
        logger.info("Batch polling service stop requested")

    async def _create_batch_with_fallback(self, prompt_type: str, article_batch: List[Dict[str, Any]]) -> Optional[Any]:
        """Create batch with API fallback and retry logic using sync APIs."""
        max_retries = 3
        retry_delay = 300  # seconds

        for attempt in range(max_retries):
            try:
                # Try OpenAI first
                batch = await self.create_batch_request(
                    'openai', prompt_type, API_MODELS['openai'], article_batch
                )
                if batch:
                    return batch

                # Fallback to Anthropic
                batch = await self.create_batch_request(
                    'anthropic', prompt_type, API_MODELS['anthropic'], article_batch
                )
                if batch:
                    return batch

                logger.warning(
                    f"Failed to create batch (attempt {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)

            except Exception as e:
                logger.error(
                    f"Error creating batch (attempt {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)

        logger.error("Failed to create batch after all retries")
        return None

    async def analyze_articles_by_batch(
        self,
        prompt_type: str,
        max_articles: Optional[int] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        filter_target_dates: bool = False,
        batch_size: int = 20
    ):
        """
        Continuously create new batches for articles that need processing.

        This method runs in a loop, periodically checking for new articles
        that need processing and creating batches for them.
        """
        logger.info(
            f"Starting continuous batch creation service for {prompt_type}")

        try:
            # Get articles to process
            articles = self.get_article_candidates(
                prompt_type, start_date, end_date, max_articles, filter_target_dates
            )

            logger.info(f"Found {len(articles)} articles to process")

            if articles:
                # Split articles into batches
                article_batches = []
                for i in range(0, len(articles), batch_size):
                    article_batches.append(
                        articles[i:i + batch_size])

                logger.info(
                    f"Creating {len(article_batches)} batches sequentially...")

                successful_batches = 0
                failed_batches = 0

                # Process batches sequentially to avoid overwhelming APIs
                for i, article_batch in enumerate(article_batches, 1):
                    if not self.running:  # Check if we should stop
                        break

                    logger.info(
                        f"Creating batch {i}/{len(article_batches)} with {len(article_batch)} articles...")

                    try:
                        batch = await self._create_batch_with_fallback(prompt_type, article_batch)
                        if batch:
                            successful_batches += 1
                            logger.info(
                                f"Successfully created batch {batch.id}, status {batch.status}")
                        else:
                            failed_batches += 1
                            logger.warning(f"Failed to create batch {i}")

                        # Small delay between batch creation to be gentle on APIs
                        await asyncio.sleep(2)

                    except Exception as e:
                        failed_batches += 1
                        logger.error(f"Error creating batch {i}: {e}")

                logger.info(
                    f"Batch creation cycle complete: {successful_batches} successful, {failed_batches} failed")
            else:
                logger.debug(f"No new articles found for {prompt_type}")

        except Exception as e:
            logger.error(f"Error in batch creation cycle: {e}")

        self.stop_polling()
        logger.info(f"Batch creation service stopped for {prompt_type}")

    @asynccontextmanager
    async def async_context(self):
        """Async context manager for proper resource cleanup."""
        try:
            yield self
        finally:
            # Cleanup resources
            logger.info("Resources cleaned up")


async def main():
    """Main function to run the analyzer from command line."""
    parser = argparse.ArgumentParser(
        description='Analyze news articles using LLM')

    parser.add_argument('-t', '--prompt-type',
                        default='influence', help='Type of analysis to perform')

    # Date filtering
    parser.add_argument('-s', '--start-date', help='Start date (YYYY-MM-DD)')
    parser.add_argument('-e', '--end-date', help='End date (YYYY-MM-DD)')
    parser.add_argument('--filter-target-dates', action='store_true',
                        help='Filter dates based on SPY price movements')

    # Resource limits
    parser.add_argument('-b', '--budget', type=float, default=0.1,
                        help='Maximum budget for API calls')
    parser.add_argument('--max-input', type=int, default=2048,
                        help='Maximum input words')
    parser.add_argument('--max-tokens', type=int, default=512,
                        help='Maximum output tokens')
    parser.add_argument('--min-input', type=int, default=200,
                        help='Minimum input words')
    parser.add_argument('--requests-per-minute', type=int, default=10,
                        help='Rate limit per minute')

    # Batch processing
    parser.add_argument('--max-articles', type=int, default=10_000,
                        help='Maximum number of articles')
    parser.add_argument('--batch-size', type=int, default=20,
                        help='Batch size')

    # Async configuration
    parser.add_argument('--polling-interval', type=int, default=DEFAULT_BATCH_POLLING_INTERVAL,
                        help='Batch polling interval in seconds')
    parser.add_argument('--poll-only-mode', action='store_true',
                        help='Run in poll-only mode (no batch creation)')

    args = parser.parse_args()

    try:
        llm_config = LLMConfig(
            max_tokens=args.max_tokens,
            max_input=args.max_input,
            min_input=args.min_input,
            temperature=0.7,
            requests_per_minute=args.requests_per_minute
        )

        db_manager = DatabaseManager()
        batch_analyzer = LLMBatchAnalyzer(llm_config, db_manager)

        current_total_cost = db_manager.llm_api_service.get_total_cost(
            prompt_type=args.prompt_type)
        if current_total_cost >= args.budget:
            logger.warning(f"Current cost {current_total_cost} exceeds budget limit "
                           f"{args.budget} for prompt type {args.prompt_type}.")
        BaseAPIManager.set_budget(args.budget, current_total_cost)

        async with batch_analyzer.async_context():
            try:
                logger.info(
                    "Starting concurrent batch creation and monitoring...")
                logger.info(
                    f"Batch polling interval: {args.polling_interval}s")
                logger.info(f"Poll only mode: {args.poll_only_mode}")

                if args.poll_only_mode:
                    logger.info("Running in poll-only mode")
                    await batch_analyzer.retrieve_and_process_batches(
                        args.prompt_type,
                        polling_interval=args.polling_interval
                    )
                else:
                    logger.info("Running in poll and create mode")

                    # Run both batch creation and batch monitoring concurrently
                    results = await asyncio.gather(
                        batch_analyzer.analyze_articles_by_batch(
                            prompt_type=args.prompt_type,
                            max_articles=args.max_articles,
                            start_date=args.start_date,
                            end_date=args.end_date,
                            filter_target_dates=args.filter_target_dates,
                            batch_size=args.batch_size
                        ),
                        batch_analyzer.retrieve_and_process_batches(
                            args.prompt_type,
                            polling_interval=args.polling_interval
                        ),
                        return_exceptions=True
                    )

                    # Check for exceptions in concurrent execution
                    for i, result in enumerate(results):
                        if isinstance(result, Exception):
                            task_name = "batch creation" if i == 0 else "batch monitoring"
                            logger.error(f"Exception in {task_name}: {result}")

                    logger.info("Concurrent batch processing completed")

            except KeyboardInterrupt:
                logger.info("Received interrupt signal, stopping polling...")
                batch_analyzer.stop_polling()
            except Exception as e:
                logger.error(f"Unexpected error during batch processing: {e}")
                batch_analyzer.stop_polling()
                raise

    except Exception as e:
        logger.error(f"Application error: {e}")


if __name__ == '__main__':
    asyncio.run(main())
