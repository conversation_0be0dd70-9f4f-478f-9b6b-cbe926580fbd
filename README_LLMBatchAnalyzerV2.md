# LLM Batch Analyzer V2 - Advanced Async Architecture

## Overview

LLMBatchAnalyzerV2 is a complete rewrite of the batch processing system with a sophisticated async architecture designed for high-performance, concurrent processing of articles through multiple LLM APIs.

## Key Features

### 🚀 **Four-Component Async Architecture**
- **Component 1: Batch Creator** - Reads articles and creates batches
- **Component 2: OpenAI Processor** - High-priority API processing  
- **Component 3: Anthropic Processor** - Lower-priority API processing
- **Component 4: Batch Monitor** - Monitors and processes completed batches

### ⚡ **Advanced Async Features**
- **Queue-based Distribution**: Thread-safe `asyncio.Queue` for batch distribution
- **API Priority System**: OpenAI gets priority access over Anthropic
- **Exponential Backoff**: Configurable retry logic with exponential delays
- **Concurrent Processing**: All components run simultaneously using `asyncio.gather()`
- **Graceful Shutdown**: Proper resource cleanup and async context management

### 🔧 **Highly Configurable**
- Batch sizes, polling intervals, timeout values
- Retry counts, backoff parameters, API priorities
- Resource limits and concurrency controls
- All parameters configurable via command line

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Batch Creator  │───▶│   asyncio.Queue  │◀───│ OpenAI Processor│
│   Component 1   │    │  (Thread-safe)   │    │   Component 2   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │Anthropic Processor│    │  Batch Monitor  │
                       │   Component 3   │    │   Component 4   │
                       └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────────────────────────────┐
                       │           Database Storage              │
                       │    (Batches, Results, Metadata)        │
                       └─────────────────────────────────────────┘
```

## Component Details

### Component 1: Batch Creator
- **Purpose**: Continuously reads articles from database and creates batches
- **Behavior**: 
  - Fetches articles that need processing
  - Groups them into configurable batch sizes
  - Puts batches into shared queue
  - Respects queue limits and timeouts

### Component 2: OpenAI Processor (High Priority)
- **Purpose**: Processes batches using OpenAI API with priority access
- **Behavior**:
  - Gets batches from queue with minimal delay (0.1s default)
  - Submits to OpenAI batch API
  - Implements exponential backoff on failures
  - Tracks active batch IDs

### Component 3: Anthropic Processor (Lower Priority)  
- **Purpose**: Processes remaining batches using Anthropic API
- **Behavior**:
  - Waits longer than OpenAI (1.0s default) to give it priority
  - Processes batches that OpenAI doesn't take
  - Same retry logic and error handling
  - Tracks active batch IDs

### Component 4: Batch Monitor
- **Purpose**: Monitors all active batches and processes results
- **Behavior**:
  - Polls active batches from both APIs
  - Downloads completed results
  - Handles batch failures and cleanup
  - Updates database with final results

## Configuration

### AsyncBatchConfig Parameters

```python
@dataclass
class AsyncBatchConfig:
    # Batch processing
    batch_size: int = 20                    # Articles per batch
    max_articles_per_cycle: int = 1000      # Max articles per creation cycle
    
    # Queue configuration  
    queue_maxsize: int = 100                # Maximum queue size
    
    # Polling intervals (seconds)
    batch_creation_interval: int = 60       # How often to check for new articles
    batch_monitoring_interval: int = 300    # How often to check batch status
    
    # Retry and backoff configuration
    max_retries: int = 3                    # Maximum retries for failed operations
    initial_backoff_delay: float = 1.0      # Initial delay in seconds
    max_backoff_delay: float = 300.0        # Maximum delay in seconds
    backoff_multiplier: float = 2.0         # Exponential backoff multiplier
    
    # API priority configuration
    openai_priority_delay: float = 0.1      # Delay to give OpenAI priority
    anthropic_delay: float = 1.0            # Additional delay for Anthropic
    
    # Timeout configuration
    api_request_timeout: float = 30.0       # Timeout for individual API requests
    batch_creation_timeout: float = 120.0   # Timeout for batch creation
    
    # Resource limits
    max_concurrent_batches: int = 50        # Maximum active batches per API
```

## Usage

### Basic Usage

```python
import asyncio
from nlp.llm.llm_batch_analyzer_v2 import LLMBatchAnalyzerV2, AsyncBatchConfig
from nlp.llm.helpers import LLMConfig
from db.database import DatabaseManager

async def main():
    # Create configurations
    llm_config = LLMConfig(
        max_tokens=512,
        max_input=2048,
        min_input=200,
        temperature=0.7,
        requests_per_minute=10
    )
    
    async_config = AsyncBatchConfig(
        batch_size=20,
        batch_creation_interval=60,
        batch_monitoring_interval=300
    )
    
    # Initialize analyzer
    db_manager = DatabaseManager()
    analyzer = LLMBatchAnalyzerV2(llm_config, async_config, db_manager)
    
    # Run async processing
    async with analyzer.async_context():
        await analyzer.run_async_batch_processing(
            prompt_type='influence',
            start_date='2024-01-01',
            end_date='2024-12-31'
        )

if __name__ == '__main__':
    asyncio.run(main())
```

### Command Line Usage

```bash
# Basic usage
python nlp/llm/llm_batch_analyzer_v2.py -t influence

# With custom configuration
python nlp/llm/llm_batch_analyzer_v2.py \
    -t influence \
    --batch-size 50 \
    --batch-creation-interval 30 \
    --batch-monitoring-interval 120 \
    --max-retries 5 \
    --openai-priority-delay 0.05 \
    --anthropic-delay 2.0

# With date filtering
python nlp/llm/llm_batch_analyzer_v2.py \
    -t influence \
    -s 2024-01-01 \
    -e 2024-12-31 \
    --filter-target-dates

# With budget and resource limits
python nlp/llm/llm_batch_analyzer_v2.py \
    -t influence \
    --budget 10.0 \
    --max-concurrent-batches 20 \
    --queue-maxsize 50
```

## Performance Benefits

### Compared to V1:
- **Concurrent Processing**: All components run simultaneously vs sequential
- **Priority Queue**: OpenAI gets priority access to batches
- **Better Resource Utilization**: Async I/O prevents blocking
- **Improved Error Handling**: Exponential backoff and retry logic
- **Graceful Shutdown**: Proper cleanup and resource management

### Expected Improvements:
- **2-3x Faster Processing**: Due to concurrent components
- **Better API Utilization**: Priority system optimizes API usage  
- **Higher Reliability**: Robust error handling and retries
- **Better Monitoring**: Real-time statistics and logging

## Monitoring and Logging

### Statistics Tracking
```python
analyzer.stats = {
    'batches_created': 0,      # Total batches created
    'batches_completed': 0,    # Successfully completed batches
    'batches_failed': 0,       # Failed batches
    'articles_processed': 0    # Total articles processed
}
```

### Logging
- Component-specific logging with prefixes
- Detailed error reporting and stack traces
- Performance metrics and timing information
- Configurable log levels and file output

## Error Handling

### Exponential Backoff
- Configurable initial delay, max delay, and multiplier
- Per-component retry logic
- Graceful degradation on persistent failures

### Graceful Shutdown
- Proper task cancellation on interrupts
- Queue cleanup and resource deallocation
- Database connection cleanup
- Final statistics reporting

## Testing

Run the test suite to verify functionality:

```bash
python test_llm_batch_analyzer_v2.py
```

Tests cover:
- Basic initialization and configuration
- Async context manager functionality  
- Queue operations and thread safety
- Error handling and edge cases

## Migration from V1

The V2 implementation maintains compatibility with existing interfaces:
- Same database schema and API contracts
- Compatible configuration parameters
- Same command-line interface (with additional options)
- Drop-in replacement for most use cases

## Future Enhancements

- **Dynamic Load Balancing**: Adjust API priorities based on performance
- **Batch Size Optimization**: Auto-tune batch sizes based on API response times
- **Advanced Monitoring**: Web dashboard for real-time monitoring
- **Multi-Region Support**: Distribute processing across multiple regions
- **Cost Optimization**: Intelligent API selection based on cost and performance
